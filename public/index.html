<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iMessage 注册检测服务</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0056b3;
            background-color: #e9ecef;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .progress-container {
            display: none;
        }
        .result-container {
            display: none;
        }
        .job-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status-processing {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        .status-completed {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status-failed {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .feature-card {
            transition: transform 0.2s;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <!-- Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h1 class="display-4 text-primary">
                    <i class="fas fa-mobile-alt me-3"></i>
                    iMessage 注册检测服务
                </h1>
                <p class="lead text-muted">批量检测电话号码是否注册了 iMessage 服务</p>
            </div>
        </div>

        <!-- Features -->
        <div class="row mb-5">
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-upload fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">文件上传</h5>
                        <p class="card-text">支持 CSV 和 TXT 格式文件，最大 10MB</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-3x text-success mb-3"></i>
                        <h5 class="card-title">批量检测</h5>
                        <p class="card-text">一次最多检测 10,000 个电话号码</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-3x text-info mb-3"></i>
                        <h5 class="card-title">结果下载</h5>
                        <p class="card-text">只返回注册了 iMessage 的号码，CSV 格式</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            上传电话号码文件
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Upload Area -->
                        <div id="uploadArea" class="upload-area">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h4>拖拽文件到这里或点击选择文件</h4>
                            <p class="text-muted">支持 CSV 和 TXT 格式，最大 10MB</p>
                            <input type="file" id="fileInput" class="d-none" accept=".csv,.txt">
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open me-2"></i>选择文件
                            </button>
                        </div>

                        <!-- File Info -->
                        <div id="fileInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file me-2"></i>
                                <span id="fileName"></span>
                                <span class="badge bg-secondary ms-2" id="fileSize"></span>
                            </div>
                            <button type="button" class="btn btn-success" id="uploadBtn">
                                <i class="fas fa-upload me-2"></i>开始检测
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" id="cancelBtn">
                                <i class="fas fa-times me-2"></i>取消
                            </button>
                        </div>

                        <!-- Progress -->
                        <div id="progressContainer" class="progress-container mt-4">
                            <h5>检测进度</h5>
                            <div class="progress mb-3">
                                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressInfo" class="row text-center">
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">总数量</h6>
                                            <h4 id="totalCount" class="text-primary">0</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">已处理</h6>
                                            <h4 id="processedCount" class="text-info">0</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">已注册</h6>
                                            <h4 id="registeredCount" class="text-success">0</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">进度</h6>
                                            <h4 id="progressPercent" class="text-warning">0%</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Results -->
                        <div id="resultContainer" class="result-container mt-4">
                            <div class="alert alert-success">
                                <h5 class="alert-heading">
                                    <i class="fas fa-check-circle me-2"></i>检测完成！
                                </h5>
                                <p id="resultSummary"></p>
                                <hr>
                                <button type="button" class="btn btn-success" id="downloadBtn">
                                    <i class="fas fa-download me-2"></i>下载结果 (CSV)
                                </button>
                                <button type="button" class="btn btn-outline-primary ms-2" id="newCheckBtn">
                                    <i class="fas fa-plus me-2"></i>新的检测
                                </button>
                            </div>
                        </div>

                        <!-- Error -->
                        <div id="errorContainer" class="mt-4" style="display: none;">
                            <div class="alert alert-danger">
                                <h5 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>检测失败
                                </h5>
                                <p id="errorMessage"></p>
                                <button type="button" class="btn btn-outline-danger" id="retryBtn">
                                    <i class="fas fa-redo me-2"></i>重试
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>使用说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>支持的文件格式：</h6>
                                <ul>
                                    <li><strong>CSV 文件：</strong>可以包含多列，系统会自动识别电话号码列</li>
                                    <li><strong>TXT 文件：</strong>每行一个电话号码</li>
                                </ul>
                                
                                <h6>电话号码格式：</h6>
                                <ul>
                                    <li>国际格式：+1234567890</li>
                                    <li>国内格式：1234567890</li>
                                    <li>带括号：(123) 456-7890</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>注意事项：</h6>
                                <ul>
                                    <li>文件大小限制：最大 10MB</li>
                                    <li>号码数量限制：最多 10,000 个</li>
                                    <li>结果只包含注册了 iMessage 的号码</li>
                                    <li>检测结果仅供参考，准确性可能受多种因素影响</li>
                                </ul>
                                
                                <div class="alert alert-warning mt-3">
                                    <small>
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        <strong>隐私提醒：</strong>请确保您有权检测这些电话号码的 iMessage 注册状态。
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p class="text-muted mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                iMessage 注册检测服务 - 安全、快速、准确
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
