{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:51:32"}
{"level":"info","message":"GET /health - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:50"}
{"level":"info","message":"GET /health - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:54"}
{"level":"info","message":"GET /api/check/+********** - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:58"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:52:36"}
{"level":"info","message":"GET /api/check/+********** - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:52:50"}
{"level":"info","message":"GET /api/check/+18175698900 - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:52:54"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:53:00"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 00:53:00"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:53:53"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 00:53:53"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:55:00"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:55:16"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:56:26"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:56:40"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:58:00"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 00:58:00"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:00:03"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 01:00:03"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 01:05:35"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:05:52"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:05:52"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:05:53"}
{"level":"info","message":"GET /health - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:06"}
{"level":"info","message":"POST /api/upload/upload - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:12"}
{"level":"info","message":"Starting to process file: test_phones.csv (Job: 03c1cb7f-200d-4d8f-80cb-2ac2f7d5a6c4)","service":"imessage-checker","timestamp":"2025-06-12 01:06:12"}
{"level":"info","message":"Found 10 phone numbers in file","service":"imessage-checker","timestamp":"2025-06-12 01:06:12"}
{"level":"info","message":"Processed batch 1, found 6 registered numbers","service":"imessage-checker","timestamp":"2025-06-12 01:06:13"}
{"level":"info","message":"Job 03c1cb7f-200d-4d8f-80cb-2ac2f7d5a6c4 completed. Found 6 registered numbers out of 10 valid numbers.","service":"imessage-checker","timestamp":"2025-06-12 01:06:13"}
{"level":"info","message":"GET /api/upload/status/03c1cb7f-200d-4d8f-80cb-2ac2f7d5a6c4 - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:19"}
{"level":"info","message":"GET /api/upload/download/03c1cb7f-200d-4d8f-80cb-2ac2f7d5a6c4 - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:25"}
{"level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:26"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:46"}
{"level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:46"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:06:46"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 01:08:43"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:09:11"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:09:11"}
{"level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:13:16"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:26"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:26"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:27"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:27"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:57"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:14:57"}
{"level":"info","message":"GET / - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:15:33"}
{"level":"info","message":"GET /app.js - ::1","service":"imessage-checker","timestamp":"2025-06-12 01:15:33"}
