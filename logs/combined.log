{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:51:32"}
{"level":"info","message":"GET /health - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:50"}
{"level":"info","message":"GET /health - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:54"}
{"level":"info","message":"GET /api/check/+********** - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:51:58"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:52:36"}
{"level":"info","message":"GET /api/check/+********** - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:52:50"}
{"level":"info","message":"GET /api/check/+18175698900 - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:52:54"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:53:00"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 00:53:00"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:53:53"}
{"level":"info","message":"Cache hit for +18175698900","service":"imessage-checker","timestamp":"2025-06-12 00:53:53"}
{"level":"info","message":"iMessage Checker Service running on port 3000","service":"imessage-checker","timestamp":"2025-06-12 00:55:00"}
{"level":"info","message":"POST /api/batch-check - ::1","service":"imessage-checker","timestamp":"2025-06-12 00:55:16"}
