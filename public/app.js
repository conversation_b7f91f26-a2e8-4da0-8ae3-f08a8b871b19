class iMessageChecker {
    constructor() {
        this.currentJobId = null;
        this.pollInterval = null;
        this.selectedFile = null;
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const newCheckBtn = document.getElementById('newCheckBtn');
        const retryBtn = document.getElementById('retryBtn');

        // File drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileSelect(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileSelect(e.target.files[0]);
            }
        });

        // Upload button
        uploadBtn.addEventListener('click', () => {
            this.uploadFile();
        });

        // Cancel button
        cancelBtn.addEventListener('click', () => {
            this.resetUpload();
        });

        // Download button
        downloadBtn.addEventListener('click', () => {
            this.downloadResults();
        });

        // New check button
        newCheckBtn.addEventListener('click', () => {
            this.resetUpload();
        });

        // Retry button
        retryBtn.addEventListener('click', () => {
            this.uploadFile();
        });
    }

    handleFileSelect(file) {
        // Validate file type
        const allowedTypes = ['text/csv', 'text/plain'];
        const allowedExtensions = ['.csv', '.txt'];
        
        if (!allowedTypes.includes(file.type) && 
            !allowedExtensions.some(ext => file.name.toLowerCase().endsWith(ext))) {
            this.showError('请选择 CSV 或 TXT 格式的文件');
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            this.showError('文件大小不能超过 10MB');
            return;
        }

        this.selectedFile = file;
        this.showFileInfo(file);
    }

    showFileInfo(file) {
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const fileInfo = document.getElementById('fileInfo');

        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        fileInfo.style.display = 'block';

        // Hide other containers
        this.hideContainers(['progressContainer', 'resultContainer', 'errorContainer']);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async uploadFile() {
        if (!this.selectedFile) {
            this.showError('请先选择文件');
            return;
        }

        const formData = new FormData();
        formData.append('phoneFile', this.selectedFile);

        try {
            this.showProgress();
            
            const response = await fetch('/api/upload/upload', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.message || '上传失败');
            }

            this.currentJobId = result.jobId;
            this.startPolling();

        } catch (error) {
            this.showError(error.message);
        }
    }

    showProgress() {
        this.hideContainers(['fileInfo', 'resultContainer', 'errorContainer']);
        document.getElementById('progressContainer').style.display = 'block';
        
        // Reset progress
        this.updateProgress(0, 0, 0, 0);
    }

    startPolling() {
        this.pollInterval = setInterval(() => {
            this.checkJobStatus();
        }, 2000); // Poll every 2 seconds
    }

    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }

    async checkJobStatus() {
        if (!this.currentJobId) return;

        try {
            const response = await fetch(`/api/upload/status/${this.currentJobId}`);
            const job = await response.json();

            if (!response.ok) {
                throw new Error(job.message || '获取状态失败');
            }

            this.updateProgress(
                job.totalNumbers,
                job.processedNumbers,
                job.registeredNumbers,
                job.progress
            );

            if (job.status === 'completed') {
                this.stopPolling();
                this.showResults(job);
            } else if (job.status === 'failed') {
                this.stopPolling();
                this.showError(job.error || '检测失败');
            }

        } catch (error) {
            this.stopPolling();
            this.showError(error.message);
        }
    }

    updateProgress(total, processed, registered, percent) {
        document.getElementById('totalCount').textContent = total.toLocaleString();
        document.getElementById('processedCount').textContent = processed.toLocaleString();
        document.getElementById('registeredCount').textContent = registered.toLocaleString();
        document.getElementById('progressPercent').textContent = percent + '%';
        
        const progressBar = document.getElementById('progressBar');
        progressBar.style.width = percent + '%';
        progressBar.setAttribute('aria-valuenow', percent);
    }

    showResults(job) {
        this.hideContainers(['progressContainer', 'errorContainer']);
        
        const resultContainer = document.getElementById('resultContainer');
        const resultSummary = document.getElementById('resultSummary');
        
        resultSummary.innerHTML = `
            检测完成！从 <strong>${job.totalNumbers.toLocaleString()}</strong> 个电话号码中
            找到 <strong>${job.registeredNumbers.toLocaleString()}</strong> 个注册了 iMessage 的号码。
        `;
        
        resultContainer.style.display = 'block';
    }

    async downloadResults() {
        if (!this.currentJobId) return;

        try {
            const response = await fetch(`/api/upload/download/${this.currentJobId}`);
            
            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.message || '下载失败');
            }

            // Create download link
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `imessage_registered_${Date.now()}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

        } catch (error) {
            this.showError(error.message);
        }
    }

    showError(message) {
        this.stopPolling();
        this.hideContainers(['progressContainer', 'resultContainer']);
        
        const errorContainer = document.getElementById('errorContainer');
        const errorMessage = document.getElementById('errorMessage');
        
        errorMessage.textContent = message;
        errorContainer.style.display = 'block';
    }

    resetUpload() {
        this.stopPolling();
        this.currentJobId = null;
        this.selectedFile = null;
        
        // Reset file input
        document.getElementById('fileInput').value = '';
        
        // Hide all containers
        this.hideContainers(['fileInfo', 'progressContainer', 'resultContainer', 'errorContainer']);
        
        // Reset upload area
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.classList.remove('dragover');
    }

    hideContainers(containerIds) {
        containerIds.forEach(id => {
            const container = document.getElementById(id);
            if (container) {
                container.style.display = 'none';
            }
        });
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new iMessageChecker();
});
