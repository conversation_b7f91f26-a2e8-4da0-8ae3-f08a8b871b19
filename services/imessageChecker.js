const axios = require('axios');
const NodeCache = require('node-cache');
const logger = require('../utils/logger');

// Cache for 1 hour (3600 seconds)
const cache = new NodeCache({ stdTTL: 3600 });

class iMessageChecker {
  constructor() {
    this.history = [];
    this.maxHistorySize = 1000;
  }

  /**
   * Check if a single phone number is registered with iMessage
   * @param {string} phoneNumber - Normalized phone number
   * @returns {Object} Check result
   */
  async checkSingle(phoneNumber) {
    try {
      // Check cache first
      const cacheKey = `imessage_${phoneNumber}`;
      const cachedResult = cache.get(cacheKey);
      
      if (cachedResult) {
        logger.info(`Cache hit for ${phoneNumber}`);
        return {
          ...cachedResult,
          source: 'cache'
        };
      }

      // Perform actual check
      const result = await this._performCheck(phoneNumber);
      
      // Cache the result
      cache.set(cacheKey, result);
      
      // Add to history
      this._addToHistory({
        phoneNumber,
        result,
        timestamp: new Date().toISOString()
      });

      return {
        ...result,
        source: 'live'
      };

    } catch (error) {
      logger.error(`Error checking ${phoneNumber}:`, error);
      return {
        phoneNumber,
        isRegistered: false,
        confidence: 0,
        lastChecked: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Check multiple phone numbers in batch
   * @param {Array} phoneNumbers - Array of normalized phone numbers
   * @param {Object} options - Batch check options
   * @returns {Array} Array of check results
   */
  async checkBatch(phoneNumbers, options = {}) {
    const {
      concurrency = 5,
      timeout = 30000,
      delayBetweenRequests = 100
    } = options;

    const results = [];
    const chunks = this._chunkArray(phoneNumbers, concurrency);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (phoneNumber) => {
        try {
          const result = await this.checkSingle(phoneNumber);
          return result;
        } catch (error) {
          logger.error(`Batch check error for ${phoneNumber}:`, error);
          return {
            phoneNumber,
            isRegistered: false,
            confidence: 0,
            lastChecked: new Date().toISOString(),
            error: error.message
          };
        }
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      
      // Process results
      chunkResults.forEach((promiseResult, index) => {
        if (promiseResult.status === 'fulfilled') {
          results.push(promiseResult.value);
        } else {
          results.push({
            phoneNumber: chunk[index],
            isRegistered: false,
            confidence: 0,
            lastChecked: new Date().toISOString(),
            error: promiseResult.reason?.message || 'Unknown error'
          });
        }
      });

      // Add delay between chunks to avoid rate limiting
      if (delayBetweenRequests > 0 && chunks.indexOf(chunk) < chunks.length - 1) {
        await this._delay(delayBetweenRequests);
      }
    }

    return results;
  }

  /**
   * Perform the actual iMessage registration check
   * This is a simplified implementation - in reality, this would need
   * to use Apple's private APIs or alternative detection methods
   * @param {string} phoneNumber 
   * @returns {Object}
   */
  async _performCheck(phoneNumber) {
    // IMPORTANT NOTE: This is a simplified implementation
    // Real iMessage detection would require:
    // 1. Access to Apple's private APIs (not publicly available)
    // 2. Alternative methods like SMS delivery status analysis
    // 3. Third-party services that provide this functionality
    
    // For demonstration purposes, we'll simulate the check
    // In a real implementation, you would:
    // - Use legitimate APIs if available
    // - Implement SMS-based detection
    // - Use carrier lookup services
    // - Analyze message delivery patterns

    try {
      // Simulate API call delay (reduced for testing)
      await this._delay(Math.random() * 500 + 100);

      // Simulate different scenarios based on phone number patterns
      const lastDigit = parseInt(phoneNumber.slice(-1));
      const isRegistered = this._simulateRegistrationCheck(phoneNumber, lastDigit);
      
      return {
        phoneNumber,
        isRegistered,
        confidence: isRegistered ? 0.85 + Math.random() * 0.15 : 0.7 + Math.random() * 0.3,
        lastChecked: new Date().toISOString(),
        method: 'simulation' // In real implementation: 'api', 'sms', 'carrier_lookup', etc.
      };

    } catch (error) {
      throw new Error(`Check failed: ${error.message}`);
    }
  }

  /**
   * Simulate iMessage registration check
   * @param {string} phoneNumber 
   * @param {number} lastDigit 
   * @returns {boolean}
   */
  _simulateRegistrationCheck(phoneNumber, lastDigit) {
    // Simulate different registration patterns
    // This is just for demonstration - replace with real logic
    
    // US numbers (+1) are more likely to have iMessage
    if (phoneNumber.startsWith('+1')) {
      return lastDigit % 3 !== 0; // ~67% registered
    }
    
    // Other countries have lower iMessage adoption
    return lastDigit % 4 === 0; // ~25% registered
  }

  /**
   * Get check history
   * @param {number} limit 
   * @param {number} offset 
   * @returns {Array}
   */
  async getHistory(limit = 50, offset = 0) {
    const start = Math.max(0, offset);
    const end = Math.min(this.history.length, start + limit);
    
    return this.history
      .slice()
      .reverse() // Most recent first
      .slice(start, end);
  }

  /**
   * Add entry to history
   * @param {Object} entry 
   */
  _addToHistory(entry) {
    this.history.push(entry);
    
    // Keep history size manageable
    if (this.history.length > this.maxHistorySize) {
      this.history = this.history.slice(-this.maxHistorySize);
    }
  }

  /**
   * Split array into chunks
   * @param {Array} array 
   * @param {number} size 
   * @returns {Array}
   */
  _chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * Delay execution
   * @param {number} ms 
   * @returns {Promise}
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clear cache
   */
  clearCache() {
    cache.flushAll();
    logger.info('Cache cleared');
  }

  /**
   * Get cache statistics
   * @returns {Object}
   */
  getCacheStats() {
    return cache.getStats();
  }
}

module.exports = new iMessageChecker();
