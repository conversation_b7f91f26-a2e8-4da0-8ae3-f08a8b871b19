const express = require('express');
const multer = require('multer');
const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const iMessageChecker = require('../services/imessageChecker');
const validator = require('../utils/validator');
const logger = require('../utils/logger');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '..', 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit for large files
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Accept CSV and TXT files
    if (file.mimetype === 'text/csv' || 
        file.mimetype === 'text/plain' || 
        file.originalname.toLowerCase().endsWith('.csv') ||
        file.originalname.toLowerCase().endsWith('.txt')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV and TXT files are allowed'), false);
    }
  }
});

// Store processing jobs in memory (in production, use Redis or database)
const processingJobs = new Map();

// Upload and process phone numbers file
router.post('/upload', upload.single('phoneFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please upload a CSV or TXT file containing phone numbers'
      });
    }

    const jobId = uuidv4();
    const filePath = req.file.path;
    
    // Initialize job status
    processingJobs.set(jobId, {
      id: jobId,
      status: 'processing',
      fileName: req.file.originalname,
      uploadTime: new Date().toISOString(),
      totalNumbers: 0,
      processedNumbers: 0,
      registeredNumbers: 0,
      progress: 0,
      resultFile: null,
      error: null
    });

    // Start processing asynchronously
    processPhoneNumbersFile(jobId, filePath, req.file.originalname);

    res.json({
      jobId,
      message: 'File uploaded successfully. Processing started.',
      status: 'processing'
    });

  } catch (error) {
    logger.error('Upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error.message
    });
  }
});

// Get job status
router.get('/status/:jobId', (req, res) => {
  const { jobId } = req.params;
  const job = processingJobs.get(jobId);

  if (!job) {
    return res.status(404).json({
      error: 'Job not found',
      message: 'The specified job ID does not exist'
    });
  }

  res.json(job);
});

// Download results
router.get('/download/:jobId', (req, res) => {
  const { jobId } = req.params;
  const job = processingJobs.get(jobId);

  if (!job) {
    return res.status(404).json({
      error: 'Job not found',
      message: 'The specified job ID does not exist'
    });
  }

  if (job.status !== 'completed') {
    return res.status(400).json({
      error: 'Job not completed',
      message: 'The job is still processing or has failed'
    });
  }

  if (!job.resultFile || !fs.existsSync(job.resultFile)) {
    return res.status(404).json({
      error: 'Result file not found',
      message: 'The result file is no longer available'
    });
  }

  const fileName = `imessage_registered_${job.fileName.replace(/\.[^/.]+$/, '')}_${Date.now()}.csv`;
  
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
  
  const fileStream = fs.createReadStream(job.resultFile);
  fileStream.pipe(res);
});

// Get all jobs (for admin/debugging)
router.get('/jobs', (req, res) => {
  const jobs = Array.from(processingJobs.values()).map(job => ({
    id: job.id,
    fileName: job.fileName,
    status: job.status,
    uploadTime: job.uploadTime,
    totalNumbers: job.totalNumbers,
    processedNumbers: job.processedNumbers,
    registeredNumbers: job.registeredNumbers,
    progress: job.progress
  }));

  res.json({
    jobs,
    total: jobs.length
  });
});

// Clean up old jobs
router.delete('/cleanup', (req, res) => {
  const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
  let cleanedCount = 0;

  for (const [jobId, job] of processingJobs.entries()) {
    if (new Date(job.uploadTime) < cutoffTime) {
      // Clean up files
      if (job.resultFile && fs.existsSync(job.resultFile)) {
        fs.unlinkSync(job.resultFile);
      }
      processingJobs.delete(jobId);
      cleanedCount++;
    }
  }

  res.json({
    message: `Cleaned up ${cleanedCount} old jobs`,
    cleanedCount
  });
});

// Process phone numbers file
async function processPhoneNumbersFile(jobId, filePath, originalFileName) {
  const job = processingJobs.get(jobId);
  
  try {
    logger.info(`Starting to process file: ${originalFileName} (Job: ${jobId})`);
    
    // Read and parse phone numbers from file
    const phoneNumbers = await readPhoneNumbersFromFile(filePath);
    
    job.totalNumbers = phoneNumbers.length;
    job.progress = 5; // 5% for file reading
    
    logger.info(`Found ${phoneNumbers.length} phone numbers in file`);
    
    if (phoneNumbers.length === 0) {
      throw new Error('No valid phone numbers found in the file');
    }

    if (phoneNumbers.length > 500000) {
      throw new Error('File contains too many phone numbers. Maximum allowed: 500,000');
    }

    // Validate phone numbers
    const validationResult = validator.validateBatch(phoneNumbers);
    const validNumbers = validationResult.validNumbers;
    
    if (validNumbers.length === 0) {
      throw new Error('No valid phone numbers found in the file');
    }

    job.progress = 10; // 10% for validation
    
    // Process in batches - larger batches for better performance
    const batchSize = 1000; // Increased batch size for large datasets
    const registeredNumbers = [];
    
    for (let i = 0; i < validNumbers.length; i += batchSize) {
      const batch = validNumbers.slice(i, i + batchSize);
      
      try {
        const results = await iMessageChecker.checkBatch(batch, {
          delayBetweenRequests: 10, // Reduced delay for faster processing
          concurrency: 20 // Increased concurrency for large datasets
        });
        
        // Filter only registered numbers
        const batchRegistered = results.filter(result => result.isRegistered);
        registeredNumbers.push(...batchRegistered);
        
        job.processedNumbers = Math.min(i + batchSize, validNumbers.length);
        job.registeredNumbers = registeredNumbers.length;
        job.progress = 10 + Math.floor((job.processedNumbers / validNumbers.length) * 80); // 10-90%
        
        logger.info(`Processed batch ${Math.floor(i/batchSize) + 1}, found ${batchRegistered.length} registered numbers`);
        
      } catch (error) {
        logger.error(`Error processing batch starting at ${i}:`, error);
        // Continue with next batch
      }
    }

    // Generate CSV file with results
    const resultFilePath = await generateResultCSV(jobId, registeredNumbers);
    
    job.resultFile = resultFilePath;
    job.status = 'completed';
    job.progress = 100;
    
    logger.info(`Job ${jobId} completed. Found ${registeredNumbers.length} registered numbers out of ${validNumbers.length} valid numbers.`);
    
    // Clean up uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
  } catch (error) {
    logger.error(`Job ${jobId} failed:`, error);
    job.status = 'failed';
    job.error = error.message;
    
    // Clean up uploaded file
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }
}

// Read phone numbers from uploaded file
async function readPhoneNumbersFromFile(filePath) {
  return new Promise((resolve, reject) => {
    const phoneNumbers = [];
    const fileExtension = path.extname(filePath).toLowerCase();
    
    if (fileExtension === '.csv') {
      // Parse CSV file
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          // Try to find phone number in any column
          const values = Object.values(row);
          for (const value of values) {
            if (value && typeof value === 'string') {
              const cleaned = value.trim();
              if (cleaned && (cleaned.includes('+') || /^\d+$/.test(cleaned))) {
                phoneNumbers.push(cleaned);
                break; // Only take first phone number from each row
              }
            }
          }
        })
        .on('end', () => {
          resolve([...new Set(phoneNumbers)]); // Remove duplicates
        })
        .on('error', reject);
    } else {
      // Parse TXT file (one phone number per line)
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          reject(err);
          return;
        }
        
        const lines = data.split('\n');
        for (const line of lines) {
          const cleaned = line.trim();
          if (cleaned && (cleaned.includes('+') || /^\d+$/.test(cleaned))) {
            phoneNumbers.push(cleaned);
          }
        }
        
        resolve([...new Set(phoneNumbers)]); // Remove duplicates
      });
    }
  });
}

// Generate CSV file with results
async function generateResultCSV(jobId, registeredNumbers) {
  const resultsDir = path.join(__dirname, '..', 'results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  const resultFilePath = path.join(resultsDir, `${jobId}_results.csv`);
  
  const csvWriter = createCsvWriter({
    path: resultFilePath,
    header: [
      { id: 'phoneNumber', title: 'Phone Number' },
      { id: 'isRegistered', title: 'iMessage Registered' },
      { id: 'confidence', title: 'Confidence' },
      { id: 'lastChecked', title: 'Last Checked' }
    ]
  });
  
  const records = registeredNumbers.map(result => ({
    phoneNumber: result.phoneNumber,
    isRegistered: result.isRegistered ? 'Yes' : 'No',
    confidence: Math.round(result.confidence * 100) + '%',
    lastChecked: result.lastChecked
  }));
  
  await csvWriter.writeRecords(records);
  return resultFilePath;
}

module.exports = router;
