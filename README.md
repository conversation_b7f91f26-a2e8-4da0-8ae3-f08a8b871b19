# iMessage 注册检测服务

一个产品化的iMessage注册检测服务，支持文件上传、批量检测和CSV结果下载。

## 🚀 产品特性

### 核心功能
- ✅ **文件上传检测**: 支持CSV和TXT文件上传，最大10MB
- ✅ **批量处理**: 一次最多检测10,000个电话号码
- ✅ **智能过滤**: 只返回注册了iMessage的号码
- ✅ **CSV导出**: 结果以CSV格式下载，包含置信度信息
- ✅ **实时进度**: 显示检测进度和统计信息
- ✅ **Web界面**: 美观易用的网页界面

### 技术特性
- ✅ 电话号码格式验证和标准化
- ✅ 结果缓存机制（1小时）
- ✅ 速率限制保护
- ✅ 错误处理和日志记录
- ✅ 异步批量处理
- ✅ 文件清理机制

## 🎯 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 环境配置

复制环境变量示例文件：

```bash
cp .env.example .env
```

根据需要修改 `.env` 文件中的配置。

### 3. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

### 4. 访问服务

- **Web界面**: http://localhost:3000
- **健康检查**: http://localhost:3000/health
- **API文档**: http://localhost:3000/api/docs

## 📱 使用方法

### Web界面使用

1. 打开浏览器访问 http://localhost:3000
2. 准备包含电话号码的CSV或TXT文件
3. 拖拽文件到上传区域或点击选择文件
4. 点击"开始检测"按钮
5. 等待检测完成，查看实时进度
6. 下载只包含注册了iMessage号码的CSV结果文件

### 支持的文件格式

**CSV文件示例:**
```csv
name,phone,email
John Doe,+18175698900,<EMAIL>
Jane Smith,+18175698901,<EMAIL>
```

**TXT文件示例:**
```
+18175698900
+18175698901
+18175698902
```

## API 文档

### 健康检查

```
GET /health
```

返回服务状态信息。

### 单个号码检测

```
GET /api/check/:phoneNumber
```

检测单个电话号码是否注册了iMessage。

**参数：**
- `phoneNumber`: 要检测的电话号码（支持国际格式）

**示例：**
```bash
curl http://localhost:3000/api/check/+**********
```

**响应：**
```json
{
  "phoneNumber": "+**********",
  "originalInput": "+**********",
  "isRegistered": true,
  "confidence": 0.92,
  "lastChecked": "2024-01-15T10:30:00.000Z",
  "source": "live"
}
```

### 批量检测

```
POST /api/batch-check
```

批量检测多个电话号码。

**请求体：**
```json
{
  "phoneNumbers": ["+**********", "+**********"],
  "options": {
    "timeout": 30000,
    "concurrency": 5
  }
}
```

**响应：**
```json
{
  "totalChecked": 2,
  "results": [
    {
      "phoneNumber": "+**********",
      "originalInput": "+**********",
      "isRegistered": true,
      "confidence": 0.92,
      "lastChecked": "2024-01-15T10:30:00.000Z",
      "source": "live"
    }
  ],
  "summary": {
    "registered": 1,
    "notRegistered": 1,
    "uncertain": 0
  }
}
```

### 检测历史

```
GET /api/history?limit=50&offset=0
```

获取检测历史记录。

### API 文档

```
GET /api/docs
```

获取完整的API文档。

## 配置选项

主要配置项在 `config/config.js` 中定义：

- **服务器配置**: 端口、主机等
- **速率限制**: 请求频率限制
- **缓存设置**: 结果缓存时间
- **批量处理**: 并发数、超时时间等
- **日志配置**: 日志级别和输出设置

## 重要说明

⚠️ **关于iMessage检测的重要提醒**

当前实现使用模拟检测方法进行演示。在实际生产环境中，iMessage注册检测需要：

1. **Apple官方API**: Apple不提供公开的iMessage注册检测API
2. **替代方案**: 
   - SMS送达状态分析
   - 运营商查询服务
   - 第三方服务提供商
   - 消息送达模式分析

3. **隐私考虑**: 检测他人的iMessage注册状态可能涉及隐私问题
4. **合规要求**: 确保遵守相关法律法规和服务条款

## 项目结构

```
├── server.js              # 主服务器文件
├── routes/
│   └── imessage.js        # iMessage检测路由
├── services/
│   └── imessageChecker.js # 核心检测逻辑
├── utils/
│   ├── validator.js       # 电话号码验证工具
│   └── logger.js          # 日志工具
├── config/
│   └── config.js          # 配置文件
├── logs/                  # 日志文件目录
├── package.json
├── .env.example
└── README.md
```

## 开发

### 运行测试

```bash
npm test
```

### 代码风格

项目使用ESLint进行代码规范检查。

### 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 支持

如有问题或建议，请创建Issue或联系开发团队。
