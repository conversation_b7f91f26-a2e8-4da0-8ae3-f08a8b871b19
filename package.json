{"name": "imessage-checker-service", "version": "1.0.0", "description": "A service to batch check if phone numbers are registered with iMessage", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["imessage", "phone", "checker", "batch", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "phone": "^3.1.40", "axios": "^1.6.2", "node-cache": "^5.1.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}