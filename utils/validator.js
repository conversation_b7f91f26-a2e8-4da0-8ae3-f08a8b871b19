const { phone } = require('phone');

class PhoneValidator {
  /**
   * Validate and normalize a phone number
   * @param {string} phoneNumber - Raw phone number input
   * @returns {Object} Validation result
   */
  validatePhoneNumber(phoneNumber) {
    try {
      // Remove any whitespace and special characters except + and digits
      const cleaned = phoneNumber.toString().trim().replace(/[^\d+]/g, '');
      
      if (!cleaned) {
        return {
          isValid: false,
          message: 'Phone number cannot be empty',
          normalizedNumber: null
        };
      }

      // Use the phone library to validate and normalize
      const result = phone(cleaned);
      
      if (result.isValid) {
        return {
          isValid: true,
          message: 'Valid phone number',
          normalizedNumber: result.phoneNumber,
          countryCode: result.countryCode,
          countryIso2: result.countryIso2,
          countryIso3: result.countryIso3
        };
      } else {
        // Try with default country code if no country code provided
        if (!cleaned.startsWith('+')) {
          // Try with US country code as default
          const withUS = phone(cleaned, { country: 'US' });
          if (withUS.isValid) {
            return {
              isValid: true,
              message: 'Valid phone number (assumed US)',
              normalizedNumber: withUS.phoneNumber,
              countryCode: withUS.countryCode,
              countryIso2: withUS.countryIso2,
              countryIso3: withUS.countryIso3,
              assumedCountry: 'US'
            };
          }
        }

        return {
          isValid: false,
          message: 'Invalid phone number format',
          normalizedNumber: null
        };
      }
    } catch (error) {
      return {
        isValid: false,
        message: `Validation error: ${error.message}`,
        normalizedNumber: null
      };
    }
  }

  /**
   * Validate an array of phone numbers
   * @param {Array} phoneNumbers - Array of phone numbers
   * @returns {Object} Batch validation result
   */
  validateBatch(phoneNumbers) {
    if (!Array.isArray(phoneNumbers)) {
      return {
        isValid: false,
        message: 'Input must be an array',
        results: []
      };
    }

    const results = phoneNumbers.map((phone, index) => ({
      index,
      original: phone,
      ...this.validatePhoneNumber(phone)
    }));

    const validResults = results.filter(r => r.isValid);
    const invalidResults = results.filter(r => !r.isValid);

    return {
      isValid: invalidResults.length === 0,
      message: `${validResults.length} valid, ${invalidResults.length} invalid`,
      results,
      validNumbers: validResults.map(r => r.normalizedNumber),
      invalidNumbers: invalidResults.map(r => ({
        original: r.original,
        reason: r.message,
        index: r.index
      })),
      summary: {
        total: phoneNumbers.length,
        valid: validResults.length,
        invalid: invalidResults.length
      }
    };
  }

  /**
   * Check if a phone number is from a specific country
   * @param {string} phoneNumber - Phone number to check
   * @param {string} countryCode - ISO2 country code (e.g., 'US', 'CA')
   * @returns {boolean}
   */
  isFromCountry(phoneNumber, countryCode) {
    const validation = this.validatePhoneNumber(phoneNumber);
    return validation.isValid && validation.countryIso2 === countryCode.toUpperCase();
  }

  /**
   * Get country information from phone number
   * @param {string} phoneNumber - Phone number
   * @returns {Object|null} Country information or null if invalid
   */
  getCountryInfo(phoneNumber) {
    const validation = this.validatePhoneNumber(phoneNumber);
    if (!validation.isValid) {
      return null;
    }

    return {
      countryCode: validation.countryCode,
      countryIso2: validation.countryIso2,
      countryIso3: validation.countryIso3
    };
  }

  /**
   * Format phone number for display
   * @param {string} phoneNumber - Phone number to format
   * @param {string} format - Format type ('international', 'national', 'e164')
   * @returns {string} Formatted phone number
   */
  formatPhoneNumber(phoneNumber, format = 'international') {
    const validation = this.validatePhoneNumber(phoneNumber);
    if (!validation.isValid) {
      return phoneNumber; // Return original if invalid
    }

    // The phone library returns E.164 format by default
    const e164 = validation.normalizedNumber;

    switch (format.toLowerCase()) {
      case 'e164':
        return e164;
      case 'national':
        // Remove country code for national format
        return e164.replace(validation.countryCode, '').replace(/^0+/, '');
      case 'international':
      default:
        // Add spaces for readability
        return e164.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, '$1 $2 $3 $4');
    }
  }

  /**
   * Check if phone number is mobile (best effort)
   * @param {string} phoneNumber - Phone number to check
   * @returns {Object} Mobile check result
   */
  isMobile(phoneNumber) {
    const validation = this.validatePhoneNumber(phoneNumber);
    if (!validation.isValid) {
      return {
        isMobile: false,
        confidence: 0,
        reason: 'Invalid phone number'
      };
    }

    // This is a simplified check - in reality, you'd need a comprehensive
    // database of mobile number ranges for each country
    const number = validation.normalizedNumber;
    
    // US mobile number patterns (simplified)
    if (validation.countryIso2 === 'US') {
      const areaCode = number.substring(2, 5);
      // Some known mobile area codes (this is not comprehensive)
      const mobileAreaCodes = ['310', '323', '424', '747', '818', '213'];
      if (mobileAreaCodes.includes(areaCode)) {
        return {
          isMobile: true,
          confidence: 0.7,
          reason: 'Area code suggests mobile'
        };
      }
    }

    return {
      isMobile: null,
      confidence: 0,
      reason: 'Cannot determine mobile status'
    };
  }
}

module.exports = new PhoneValidator();
