require('dotenv').config();

const config = {
  // Server configuration
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    environment: process.env.NODE_ENV || 'development'
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // limit each IP to 100 requests per windowMs
    message: {
      error: 'Too many requests from this IP, please try again later.'
    }
  },

  // Cache configuration
  cache: {
    ttl: parseInt(process.env.CACHE_TTL) || 3600, // 1 hour in seconds
    checkPeriod: parseInt(process.env.CACHE_CHECK_PERIOD) || 600 // 10 minutes
  },

  // iMessage checker configuration
  imessageChecker: {
    // Batch processing settings
    batch: {
      maxSize: parseInt(process.env.BATCH_MAX_SIZE) || 1000, // Increased for large datasets
      concurrency: parseInt(process.env.BATCH_CONCURRENCY) || 20, // Higher concurrency
      timeout: parseInt(process.env.BATCH_TIMEOUT) || 60000, // 60 seconds for large batches
      delayBetweenRequests: parseInt(process.env.BATCH_DELAY) || 10 // Reduced delay
    },

    // History settings
    history: {
      maxSize: parseInt(process.env.HISTORY_MAX_SIZE) || 1000
    },

    // Check method configuration
    methods: {
      // Primary method to use for checking
      primary: process.env.IMESSAGE_CHECK_METHOD || 'simulation',
      
      // Fallback methods if primary fails
      fallback: (process.env.IMESSAGE_FALLBACK_METHODS || 'simulation').split(','),
      
      // Timeout for each check method
      timeout: parseInt(process.env.IMESSAGE_CHECK_TIMEOUT) || 10000 // 10 seconds
    }
  },

  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED !== 'false',
      maxSize: process.env.LOG_FILE_MAX_SIZE || '5m',
      maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES) || 5
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false'
    }
  },

  // Security configuration
  security: {
    helmet: {
      enabled: process.env.HELMET_ENABLED !== 'false'
    },
    cors: {
      enabled: process.env.CORS_ENABLED !== 'false',
      origin: process.env.CORS_ORIGIN || '*',
      credentials: process.env.CORS_CREDENTIALS === 'true'
    }
  },

  // API configuration
  api: {
    version: process.env.API_VERSION || 'v1',
    prefix: process.env.API_PREFIX || '/api',
    documentation: {
      enabled: process.env.API_DOCS_ENABLED !== 'false'
    }
  },

  // External services configuration (for future use)
  externalServices: {
    // Apple iMessage API (if available)
    apple: {
      apiKey: process.env.APPLE_API_KEY,
      baseUrl: process.env.APPLE_API_BASE_URL,
      timeout: parseInt(process.env.APPLE_API_TIMEOUT) || 10000
    },

    // SMS gateway for verification (if needed)
    sms: {
      provider: process.env.SMS_PROVIDER || 'twilio',
      apiKey: process.env.SMS_API_KEY,
      apiSecret: process.env.SMS_API_SECRET,
      timeout: parseInt(process.env.SMS_TIMEOUT) || 30000
    },

    // Phone number lookup services
    phoneValidation: {
      provider: process.env.PHONE_VALIDATION_PROVIDER || 'numverify',
      apiKey: process.env.PHONE_VALIDATION_API_KEY,
      timeout: parseInt(process.env.PHONE_VALIDATION_TIMEOUT) || 5000
    }
  }
};

// Validation function
function validateConfig() {
  const errors = [];

  // Validate required environment variables for production
  if (config.server.environment === 'production') {
    const requiredVars = [
      // Add required environment variables here
    ];

    requiredVars.forEach(varName => {
      if (!process.env[varName]) {
        errors.push(`Missing required environment variable: ${varName}`);
      }
    });
  }

  // Validate numeric values
  if (config.server.port < 1 || config.server.port > 65535) {
    errors.push('PORT must be between 1 and 65535');
  }

  if (config.imessageChecker.batch.maxSize > 10000) {
    errors.push('BATCH_MAX_SIZE cannot exceed 10,000');
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
}

// Validate configuration on load
try {
  validateConfig();
} catch (error) {
  console.error('Configuration Error:', error.message);
  process.exit(1);
}

module.exports = config;
