# Server Configuration
PORT=3000
HOST=localhost
NODE_ENV=development

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100

# Cache Configuration
CACHE_TTL=3600
CACHE_CHECK_PERIOD=600

# Batch Processing (Optimized for large datasets)
BATCH_MAX_SIZE=1000
BATCH_CONCURRENCY=20
BATCH_TIMEOUT=60000
BATCH_DELAY=10

# History
HISTORY_MAX_SIZE=1000

# iMessage Check Configuration
IMESSAGE_CHECK_METHOD=simulation
IMESSAGE_FALLBACK_METHODS=simulation
IMESSAGE_CHECK_TIMEOUT=10000

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_MAX_SIZE=5m
LOG_FILE_MAX_FILES=5
LOG_CONSOLE_ENABLED=true

# Security
HELMET_ENABLED=true
CORS_ENABLED=true
CORS_ORIGIN=*
CORS_CREDENTIALS=false

# API Configuration
API_VERSION=v1
API_PREFIX=/api
API_DOCS_ENABLED=true

# External Services (Optional)
# Apple API (if available)
APPLE_API_KEY=
APPLE_API_BASE_URL=
APPLE_API_TIMEOUT=10000

# SMS Gateway (for verification)
SMS_PROVIDER=twilio
SMS_API_KEY=
SMS_API_SECRET=
SMS_TIMEOUT=30000

# Phone Validation Service
PHONE_VALIDATION_PROVIDER=numverify
PHONE_VALIDATION_API_KEY=
PHONE_VALIDATION_TIMEOUT=5000
