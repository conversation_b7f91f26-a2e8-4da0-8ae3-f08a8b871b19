const fs = require('fs');
const path = require('path');

// 读取原文件
const inputFile = '20000.txt';
const outputFile = '20000_with_plus.txt';

console.log('开始处理文件...');

try {
    // 读取文件内容
    const data = fs.readFileSync(inputFile, 'utf8');
    
    // 按行分割
    const lines = data.split('\n');
    
    // 给每行添加+号（跳过空行）
    const processedLines = lines.map(line => {
        const trimmed = line.trim();
        if (trimmed && /^\d+$/.test(trimmed)) {
            return '+' + trimmed;
        }
        return trimmed; // 保留空行或非数字行
    }).filter(line => line); // 移除空行
    
    // 写入新文件
    fs.writeFileSync(outputFile, processedLines.join('\n') + '\n');
    
    console.log(`处理完成！`);
    console.log(`原文件: ${inputFile} (${lines.length} 行)`);
    console.log(`新文件: ${outputFile} (${processedLines.length} 行)`);
    console.log(`已为所有电话号码添加 + 号`);
    
    // 显示前几行作为示例
    console.log('\n前10行示例:');
    processedLines.slice(0, 10).forEach((line, index) => {
        console.log(`${index + 1}: ${line}`);
    });
    
} catch (error) {
    console.error('处理文件时出错:', error.message);
}
