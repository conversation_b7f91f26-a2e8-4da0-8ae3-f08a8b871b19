{"name": "phone", "version": "3.1.59", "description": "With a given country and phone number, validate and format the phone number to E.164 standard", "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=12"}, "directories": {"lib": "./src/lib"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:update": "jest --updateSnapshot", "lint": "eslint .", "build": "rimraf ./dist && tsc", "start:example": "yarn build && webpack serve --config ./example/webpack.config.js --progress", "dev": "yarn start:example", "prepublishOnly": "yarn build", "fix:numbers": "ts-node -P scripts/add-new-rules/tsconfig.json scripts/add-new-rules"}, "repository": {"type": "git", "url": "https://github.com/aftership/phone"}, "bugs": {"url": "https://github.com/aftership/phone/issues"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/papaparse": "^5.3.15", "babel-cli": "^6.26.0", "babel-loader": "^10.0.0", "babel-minify-webpack-plugin": "^0.3.1", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "dotenv": "^16.5.0", "eslint": "^9.24.0", "eslint-config-aftership": "^7.0.0", "eslint-plugin-import": "^2.31.0", "idempotent-babel-polyfill": "^7.4.4", "jest": "^29.7.0", "lodash": "^4.17.21", "papaparse": "^5.5.2", "rimraf": "^6.0.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "twilio": "^5.5.2", "typescript": "^5.8.3", "webpack": "^5.99.5", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "keywords": ["phone", "e.164"], "author": "AfterShip", "license": "MIT", "dependencies": {}}