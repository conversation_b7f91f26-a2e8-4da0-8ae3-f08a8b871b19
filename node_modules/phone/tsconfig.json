{
  "compilerOptions": {
    "target": "es6",
    "module": "commonjs",
    "lib": [
      "es6",
      "dom"
    ],
    "esModuleInterop": true,
    "allowJs": true,
    "declaration": true,                         /* Generates corresponding '.d.ts' file. */
    "outDir": "./dist",                              /* Redirect output structure to the directory. */
    "strict": true,                                 /* Enable all strict type-checking options. */
    "noUnusedLocals": true,                      /* Report errors on unused locals. */
    "noUnusedParameters": true,                  /* Report errors on unused parameters. */
    "noImplicitReturns": false,                   /* Report error when not all code paths in function return a value. */
  },
  "include": ["src"],
  "exclude": ["node_modules", "**/__tests__/*"]
}